import pandas as pd
import argparse # 导入 argparse 模块
import json
import os

class ExcelReader:
    """
    此类用于将 Excel 文件中的数据转换为特定的 JSON 结构。
    """
    def __init__(self, excel_file_path: str):
        """
        初始化转换器。

        Args:
            excel_file_path: Excel 文件的路径。
        """
        self.excel_file_path = excel_file_path

    def read_excel(self, sheet_name: str, base_sheet_name: str = None) -> list:
        """
        读取 Excel 文件。

        Args:
            sheet_name: 要读取的工作表名称。
            base_sheet_name: 基础工作表名称，默认为 None。如果指定了基础工作表名称，会先加载基础工作表，然后把sheet_name中的数据合并到基础工作表中。
        """
        try:
            sheet_data = pd.read_excel(self.excel_file_path, sheet_name=sheet_name)
            print(f"成功读取 Excel 文件: {self.excel_file_path}")
            
            if base_sheet_name is not None:
                base_sheet_data = pd.read_excel(self.excel_file_path, sheet_name=base_sheet_name)
        except FileNotFoundError:
            print(f"错误: 文件未找到 {self.excel_file_path}")
            return
        except Exception as e:
            print(f"读取 Excel 文件时出错: {e}")
            return

        # 是否指定了base表用来合并
        if base_sheet_name is not None:
            # 阶段1：处理基础数据
            base_processed_data = self._process_rows(base_sheet_data)

            # 阶段2：处理增量数据
            diff_processed_data = self._process_rows(sheet_data)

            # 阶段3：智能合并两个树结构
            processed_data = self._merge_tree_data(base_processed_data, diff_processed_data)
        else:
            # 单sheet处理
            processed_data = self._process_rows(sheet_data)

        # 最后阶段：统一分配key_path编号
        if processed_data:
            self._assign_key_paths(processed_data)

        return processed_data

    def _merge_tree_data(self, base_processed_data, diff_processed_data):
        """
        智能合并两个树结构，采用overwrite策略。
        使用迭代方式，直接修改base_processed_data。

        Args:
            base_processed_data: 基础树结构数据
            diff_processed_data: 增量树结构数据

        Returns:
            合并后的树结构数据（即修改后的base_processed_data）
        """
        # 1. 迭代提取diff中所有答案节点的路径和candidates
        answer_nodes_to_merge = []
        queue = [(node, [node.get("category_desc", "")]) for node in diff_processed_data]

        while queue:
            current_node, path = queue.pop(0)

            # 如果当前节点有candidates，记录下来
            if "candidates" in current_node:
                answer_nodes_to_merge.append((path, current_node["candidates"]))

            # 将子节点加入队列
            if "sub_category" in current_node and isinstance(current_node["sub_category"], list):
                for sub_node in current_node["sub_category"]:
                    new_path = path + [sub_node.get("category_desc", "")]
                    queue.append((sub_node, new_path))

        # 2. 对每个答案节点，在base中查找或创建对应路径，然后overwrite
        for path, candidates in answer_nodes_to_merge:
            target_node = self._find_or_create_category_path(base_processed_data, path)
            # overwrite策略：直接替换candidates
            target_node["candidates"] = candidates

        print(f"合并完成，共处理了 {len(answer_nodes_to_merge)} 个答案节点。")
        return base_processed_data

    def _process_row(self, row):
        """
        处理单行数据。
        (此处的具体解析和转换逻辑待实现)
        """

        row_values = []
        # 遍历，前5列是分类，第6列是答案，第7列是问题示例
        classify_columns = 5
        for col in range(classify_columns + 2):
            # 使用 .iloc 进行位置索引，避免 FutureWarning
            cell_value = row.iloc[col]
            # 使用 pd.notna() 检查是否为有效值 (非 None 和非 NaN)
            if pd.notna(cell_value):
                # 先转换为字符串，再去除首尾空格
                cell_value_str = str(cell_value).strip()
                if len(cell_value_str) > 0:
                    row_values.append(cell_value_str)
                    continue
            
            # 分类列之后，即使是空值也要添加空字符串
            if col >= classify_columns:
                row_values.append("")

        return row_values



    def _assign_key_paths(self, processed_data, parent_key_path=""):
        """
        统一为整个分类树分配key_path编号。

        Args:
            processed_data: 分类树数据列表
            parent_key_path: 父节点的key_path前缀（如""、"1."、"1.1."）
        """
        for index, node in enumerate(processed_data):
            # 为分类节点生成key_path（以.结尾）
            if parent_key_path == "":
                # 根节点
                node_key_path = f"{index + 1}."
            else:
                # 子节点：parent_key_path + 当前索引 + "."
                node_key_path = f"{parent_key_path}{index + 1}."

            node["key_path"] = node_key_path

            # 递归处理子分类
            if "sub_category" in node and isinstance(node["sub_category"], list):
                # 使用当前节点的key_path作为子节点的parent_key_path
                self._assign_key_paths(node["sub_category"], node_key_path)

            # 为candidates分配key_path
            if "candidates" in node and isinstance(node["candidates"], list):
                # 计算candidates的起始编号：子分类数量 + 1
                sub_category_count = len(node.get("sub_category", []))
                base_path = node_key_path.rstrip('.')  # 移除末尾的.

                for candidate_index, candidate in enumerate(node["candidates"]):
                    candidate_key_path = f"{base_path}.{sub_category_count + 1 + candidate_index}"
                    candidate["key_path"] = candidate_key_path

    def _process_rows(self, sheet_data):
        """
        遍历并处理 Excel 文件中的所有行。
        """
        if sheet_data is None:
            print("错误: 数据未加载，请先调用 read_excel()")
            return

        # 直接在遍历时构建 processed_data
        processed_data = []
        for _, row in sheet_data.iterrows():
            processed_row_data = self._process_row(row)
            if processed_row_data:
                # ---- 将合并逻辑移到此处 ----
                if len(processed_row_data) < 3:
                    print(f"警告: 跳过无效行 (至少包含1个分类、1个答案、1个问题示例): {processed_row_data}")
                    continue

                # 处理7列数据结构：前N-2列为keys，倒数第2列为answer，倒数第1列为question_example
                keys = processed_row_data[:-2]
                answer = processed_row_data[-2]
                question_example = processed_row_data[-1]  # question_example（可能为空字符串）

                current_level = processed_data # 从根列表开始

                for i, key in enumerate(keys):
                    found_node = None
                    # 在当前层级查找具有相同 category_desc 的节点
                    for node in current_level:
                        if node.get("category_desc") == key:
                            found_node = node
                            break

                    is_last_key = (i == len(keys) - 1)

                    if found_node:
                        # 找到节点
                        if is_last_key:
                            # 如果是最后一个 key，添加到 candidates 数组
                            if "candidates" not in found_node:
                                found_node["candidates"] = []

                            found_node["candidates"].append({
                                "answer": answer,
                                "question_example": question_example
                            })
                        else:
                            # 如果不是最后一个 key，确保有 sub_category 并进入下一层
                            if "sub_category" not in found_node:
                                found_node["sub_category"] = []
                            # 检查 current_level 是否为 None 或不是列表，如果需要则创建
                            if not isinstance(found_node.get("sub_category"), list):
                                found_node["sub_category"] = []
                            current_level = found_node["sub_category"]
                    else:
                        # 未找到节点，创建新节点
                        new_node = {
                            "category_desc": key
                        }
                        current_level.append(new_node) # 将新节点添加到当前列表

                        if is_last_key:
                            # 如果是最后一个 key，创建 candidates 数组
                            new_node["candidates"] = [{
                                "answer": answer,
                                "question_example": question_example
                            }]
                        else:
                            # 如果不是最后一个 key，添加空的 sub_category 列表，并进入下一层
                            new_node["sub_category"] = []
                            current_level = new_node["sub_category"]
                # ---- 合并逻辑结束 ----

        print("数据结构构建完成。")
        return processed_data

    def _find_or_create_category_path(self, tree_data, path):
        """
        迭代方式查找或创建分类路径，返回目标节点。

        Args:
            tree_data: 树结构数据列表
            path: 分类路径列表，例如 ["账号", "密码相关", "找回密码"]

        Returns:
            目标分类节点（字典）
        """
        current_level = tree_data
        current_node = None

        for i, category_desc in enumerate(path):
            # 在当前层级查找具有相同 category_desc 的节点
            found_node = None
            for node in current_level:
                if node.get("category_desc") == category_desc:
                    found_node = node
                    break

            if found_node:
                # 找到节点，继续下一层
                current_node = found_node
                if i < len(path) - 1:  # 不是最后一层
                    # 确保有 sub_category 并进入下一层
                    if "sub_category" not in current_node:
                        current_node["sub_category"] = []
                    current_level = current_node["sub_category"]
            else:
                # 未找到节点，创建新节点
                new_node = {
                    "category_desc": category_desc
                }
                current_level.append(new_node)
                current_node = new_node

                if i < len(path) - 1:  # 不是最后一层
                    # 添加空的 sub_category 列表，并进入下一层
                    new_node["sub_category"] = []
                    current_level = new_node["sub_category"]

        return current_node

    def dump_processed_data(self, processed_data, output_file_path: str):
        if output_file_path is not None and len(output_file_path) > 0:
            # 写入文件
            with open(output_file_path, "w", encoding="utf-8") as f:
                json.dump(processed_data, f, indent=4, ensure_ascii=False)
            print(f"数据已保存到 {output_file_path}")
        else:
            # 打印到控制台
            print(json.dumps(processed_data, indent=4, ensure_ascii=False))


class ExcelWriter:
    """
    此类用于将 FAQ JSON 数据转换为 Excel 文件。
    """
    def __init__(self, json_file_path: str):
        """
        初始化写入器。

        Args:
            json_file_path: JSON 文件的路径。
        """
        self.json_file_path = json_file_path
        self.faq_data = None

    def load_json(self):
        """
        加载 JSON 文件。
        """
        try:
            with open(self.json_file_path, 'r', encoding='utf-8') as f:
                self.faq_data = json.load(f)
            print(f"成功加载 JSON 文件: {self.json_file_path}")
        except FileNotFoundError:
            print(f"错误: 文件未找到 {self.json_file_path}")
            return False
        except Exception as e:
            print(f"加载 JSON 文件时出错: {e}")
            return False
        return True

    def _extract_rows_from_node(self, node, parent_desc_path=None):
        """
        从节点中提取行数据。

        Args:
            node: 当前节点
            parent_desc_path: 父节点的描述路径列表

        Returns:
            包含行数据的列表
        """
        rows = []

        # 构建当前节点的描述路径
        current_desc_path = (parent_desc_path or []) + [node.get("category_desc", "")]

        # 如果当前节点有candidates，则为每个candidate添加一行
        if "candidates" in node and isinstance(node["candidates"], list):
            for candidate in node["candidates"]:
                row = {
                    "key_path": candidate.get("key_path", ""),
                    "category_desc_path": " <<< ".join(current_desc_path),
                    "answer": candidate.get("answer", ""),
                    "question_example": candidate.get("question_example", "")
                }
                rows.append(row)

        # 递归处理子分类
        if "sub_category" in node and isinstance(node["sub_category"], list):
            for sub_node in node["sub_category"]:
                rows.extend(self._extract_rows_from_node(sub_node, current_desc_path))

        return rows

    def write_excel(self, output_file_path: str):
        """
        将 JSON 数据写入 Excel 文件。

        Args:
            output_file_path: 输出的 Excel 文件路径
        """
        if self.faq_data is None:
            print("错误: 请先调用 load_json() 加载数据")
            return False

        try:
            # 提取所有行数据
            all_rows = []
            for root_node in self.faq_data:
                all_rows.extend(self._extract_rows_from_node(root_node))

            if not all_rows:
                print("警告: 没有找到包含candidates的节点")
                return False

            # 创建DataFrame
            df = pd.DataFrame(all_rows)

            # 确保输出目录存在
            output_dir = os.path.dirname(output_file_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 写入Excel文件
            df.to_excel(output_file_path, index=False, engine='openpyxl')

            print(f"成功写入 Excel 文件: {output_file_path}")
            print(f"共写入 {len(all_rows)} 行数据")
            return True

        except Exception as e:
            print(f"写入 Excel 文件时出错: {e}")
            return False

# 示例用法
if __name__ == "__main__":
    # 设置命令行参数解析器
    parser = argparse.ArgumentParser(
        description='Excel 和 JSON 之间的双向转换工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # Excel 转 JSON
  python excel_converter.py excel-to-json faq.xlsx --output faq_doc.json

  # JSON 转 Excel
  python excel_converter.py json-to-excel faq_doc.json --output faq_output.xlsx
        """
    )

    # 添加子命令
    subparsers = parser.add_subparsers(dest='command', help='操作模式')

    # Excel 转 JSON 子命令
    excel_to_json_parser = subparsers.add_parser(
        'excel-to-json',
        help='将 Excel 文件转换为 JSON 格式'
    )
    excel_to_json_parser.add_argument(
        'excel_file',
        type=str,
        help='要转换的 Excel 文件路径'
    )
    excel_to_json_parser.add_argument(
        '--sheet',
        default=0,
        help='要读取的工作表名称或索引 (默认为 0)'
    )
    excel_to_json_parser.add_argument(
        '--base-sheet',
        default=None,
        help='基础工作表名称，用于合并数据 (默认为 None)'
    )
    excel_to_json_parser.add_argument(
        '--output',
        required=True,
        help='输出的 JSON 文件路径'
    )

    # JSON 转 Excel 子命令
    json_to_excel_parser = subparsers.add_parser(
        'json-to-excel',
        help='将 JSON 文件转换为 Excel 格式'
    )
    json_to_excel_parser.add_argument(
        'json_file',
        type=str,
        help='要转换的 JSON 文件路径'
    )
    json_to_excel_parser.add_argument(
        '--output',
        required=True,
        help='输出的 Excel 文件路径'
    )

    # 解析命令行参数
    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        exit(1)

    # 根据命令执行相应操作
    if args.command == 'excel-to-json':
        # Excel 转 JSON
        reader = ExcelReader(args.excel_file)
        processed_data = reader.read_excel(sheet_name=args.sheet, base_sheet_name=args.base_sheet)
        if processed_data is not None:
            reader.dump_processed_data(processed_data, args.output)

    elif args.command == 'json-to-excel':
        # JSON 转 Excel
        writer = ExcelWriter(args.json_file)
        if writer.load_json():
            writer.write_excel(args.output)
