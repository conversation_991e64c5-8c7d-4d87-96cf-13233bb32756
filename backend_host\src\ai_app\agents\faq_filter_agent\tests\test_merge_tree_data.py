#!/usr/bin/env python3
"""
测试 _merge_tree_data 函数的实现
"""

import sys
import os
import json

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../..'))
sys.path.insert(0, project_root)

from ai_app.agents.faq_filter_agent.data.excel_converter import ExcelReader

def test_merge_tree_data():
    """测试 _merge_tree_data 函数的基本功能"""
    
    # 创建测试用的 ExcelReader 实例
    reader = ExcelReader("dummy_path")  # 路径不重要，我们直接测试函数
    
    # 测试数据1：base_processed_data
    base_data = [
        {
            "category_desc": "账号",
            "sub_category": [
                {
                    "category_desc": "密码相关",
                    "candidates": [
                        {
                            "answer": "原始密码答案",
                            "question_example": "原始密码问题"
                        }
                    ]
                },
                {
                    "category_desc": "实名认证",
                    "candidates": [
                        {
                            "answer": "原始实名认证答案",
                            "question_example": "原始实名认证问题"
                        }
                    ]
                }
            ]
        }
    ]
    
    # 测试数据2：diff_processed_data
    diff_data = [
        {
            "category_desc": "账号",
            "sub_category": [
                {
                    "category_desc": "密码相关",
                    "candidates": [
                        {
                            "answer": "更新后的密码答案",
                            "question_example": "更新后的密码问题"
                        }
                    ]
                },
                {
                    "category_desc": "新功能",
                    "candidates": [
                        {
                            "answer": "新功能答案",
                            "question_example": "新功能问题"
                        }
                    ]
                }
            ]
        },
        {
            "category_desc": "充值",
            "candidates": [
                {
                    "answer": "充值答案",
                    "question_example": "充值问题"
                }
            ]
        }
    ]
    
    print("=== 测试开始 ===")
    print("Base数据:")
    print(json.dumps(base_data, indent=2, ensure_ascii=False))
    print("\nDiff数据:")
    print(json.dumps(diff_data, indent=2, ensure_ascii=False))
    
    # 执行合并
    result = reader._merge_tree_data(base_data, diff_data)
    
    print("\n=== 合并结果 ===")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    # 验证结果
    print("\n=== 验证结果 ===")
    
    # 验证1：密码相关应该被overwrite
    account_node = None
    for node in result:
        if node.get("category_desc") == "账号":
            account_node = node
            break
    
    if account_node:
        password_node = None
        for sub_node in account_node.get("sub_category", []):
            if sub_node.get("category_desc") == "密码相关":
                password_node = sub_node
                break
        
        if password_node and "candidates" in password_node:
            password_answer = password_node["candidates"][0]["answer"]
            if password_answer == "更新后的密码答案":
                print("✓ 密码相关节点成功overwrite")
            else:
                print("✗ 密码相关节点overwrite失败")
        else:
            print("✗ 未找到密码相关节点的candidates")
    else:
        print("✗ 未找到账号节点")
    
    # 验证2：实名认证应该保持不变
    if account_node:
        auth_node = None
        for sub_node in account_node.get("sub_category", []):
            if sub_node.get("category_desc") == "实名认证":
                auth_node = sub_node
                break
        
        if auth_node and "candidates" in auth_node:
            auth_answer = auth_node["candidates"][0]["answer"]
            if auth_answer == "原始实名认证答案":
                print("✓ 实名认证节点保持不变")
            else:
                print("✗ 实名认证节点被意外修改")
        else:
            print("✗ 未找到实名认证节点的candidates")
    
    # 验证3：新功能节点应该被添加
    if account_node:
        new_feature_node = None
        for sub_node in account_node.get("sub_category", []):
            if sub_node.get("category_desc") == "新功能":
                new_feature_node = sub_node
                break
        
        if new_feature_node and "candidates" in new_feature_node:
            print("✓ 新功能节点成功添加")
        else:
            print("✗ 新功能节点添加失败")
    
    # 验证4：充值节点应该被添加到根级别
    charge_node = None
    for node in result:
        if node.get("category_desc") == "充值":
            charge_node = node
            break
    
    if charge_node and "candidates" in charge_node:
        print("✓ 充值节点成功添加到根级别")
    else:
        print("✗ 充值节点添加失败")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_merge_tree_data()
